package dev.pigmomo.yhkit2025.service.productmonitor

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.core.app.NotificationCompat
import dev.pigmomo.yhkit2025.MainActivity
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

/**
 * 监控通知服务
 * 用于发送监控变化通知
 */
object MonitoringNotificationService {

    private const val TAG = "MonitoringNotificationService"
    private const val CHANNEL_ID = "monitoring_changes"
    private const val CHANNEL_NAME = "监控变化通知"
    private const val NOTIFICATION_ID_BASE = 2000

    private val notificationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    // 通知事件流
    private val _notificationEvents = MutableSharedFlow<NotificationEvent>(
        replay = 0,
        extraBufferCapacity = 10
    )
    val notificationEvents: SharedFlow<NotificationEvent> = _notificationEvents.asSharedFlow()

    /**
     * 通知事件类型
     */
    sealed class NotificationEvent {
        data class ProductChanged(
            val product: ProductMonitorEntity,
            val changes: List<ProductChangeRecordEntity>
        ) : NotificationEvent()
    }

    /**
     * 初始化通知渠道
     */
    fun initializeNotificationChannel(context: Context) {
        val notificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        val channel = NotificationChannel(
            CHANNEL_ID,
            CHANNEL_NAME,
            NotificationManager.IMPORTANCE_DEFAULT
        ).apply {
            description = "商品监控变化通知"
            setShowBadge(true)
            enableVibration(true)
            enableLights(true)
        }

        notificationManager.createNotificationChannel(channel)
        Log.d(TAG, "Notification channel created: $CHANNEL_ID")
    }

    /**
     * 监听监控事件并发送通知
     */
    fun startListening(context: Context) {
        notificationScope.launch {
            MonitoringEventBus.events.collect { event ->
                Log.d(TAG, "收到监控事件: ${event::class.simpleName}")
                when (event) {
                    is MonitoringEventBus.MonitoringEvent.TaskExecuted -> {
                        Log.d(
                            TAG,
                            "处理任务执行完成事件: planId=${event.planId}, success=${event.result.success}, changes=${event.result.changesDetected}"
                        )
                        handleTaskExecuted(context, event.result)
                    }

                    else -> {
                        Log.d(TAG, "忽略其他事件: ${event::class.simpleName}")
                    }
                }
            }
        }
        Log.d(TAG, "Started listening for monitoring events")
    }

    /**
     * 处理单个任务执行完成事件
     */
    private suspend fun handleTaskExecuted(context: Context, result: MonitoringTaskResult) {
        Log.d(
            TAG,
            "处理任务执行结果: success=${result.success}, changesDetected=${result.changesDetected}, productChanges=${result.productChanges.size}"
        )

        if (result.success && result.changesDetected > 0) {
            Log.d(TAG, "Task executed with ${result.changesDetected} changes detected")

            // 只发送具体的商品变化通知
            if (result.productChanges.isNotEmpty()) {
                Log.d(TAG, "发送 ${result.productChanges.size} 个商品的变化通知")
                result.productChanges.forEach { (product, changes) ->
                    Log.d(TAG, "发送商品「${product.title}」的变化通知，变化数量: ${changes.size}")
                    sendDetailedProductChangeNotification(context, product, changes)
                }
            } else {
                Log.d(TAG, "没有具体的商品变化信息，跳过通知发送")
            }
        } else {
            Log.d(TAG, "任务执行失败或无变化，跳过通知发送")
        }
    }


    /**
     * 发送变化通知
     */
    private fun sendChangeNotification(
        context: Context,
        title: String,
        content: String,
        changeCount: Int
    ) {
        try {
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // 创建点击意图
            val intent = Intent(context, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }

            val pendingIntent = PendingIntent.getActivity(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // 构建通知
            val notification = NotificationCompat.Builder(context, CHANNEL_ID)
                .setContentTitle(title)
                .setContentText(content)
                .setStyle(NotificationCompat.BigTextStyle().bigText(content))
                .setSmallIcon(R.drawable.ic_launcher_foreground)
                //.setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setNumber(changeCount)
                .build()

            // 使用时间戳作为通知ID，确保每个通知都能显示
            val notificationId = NOTIFICATION_ID_BASE + (System.currentTimeMillis() % 1000).toInt()
            notificationManager.notify(notificationId, notification)

            Log.d(TAG, "Change notification sent: $title - $content")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to send change notification", e)
        }
    }

    /**
     * 发送详细的商品变化通知（内部方法）
     */
    private fun sendDetailedProductChangeNotification(
        context: Context,
        product: ProductMonitorEntity,
        changes: List<ProductChangeRecordEntity>
    ) {
        val title = product.title
        val content = buildString {
            changes.take(3).forEachIndexed { index, change ->
                append(formatChangeInfo(change))
                // 添加换行符（除了最后一个）
                if (index < minOf(2, changes.size - 1)) {
                    append("\n")
                }
            }
            // 如果变化超过3个，显示剩余数量
            if (changes.size > 3) {
                append("\n还有 ${changes.size - 3} 个变化")
            }
        }

        sendChangeNotification(context, title, content, changes.size)

        // 发送商品变化事件
        notificationScope.launch {
            _notificationEvents.tryEmit(
                NotificationEvent.ProductChanged(product, changes)
            )
        }
    }

    /**
     * 发送特定商品变化通知（公共接口）
     */
    fun sendProductChangeNotification(
        context: Context,
        product: ProductMonitorEntity,
        changes: List<ProductChangeRecordEntity>
    ) {
        sendDetailedProductChangeNotification(context, product, changes)
    }

    /**
     * 格式化变化信息
     */
    private fun formatChangeInfo(change: ProductChangeRecordEntity): String {
        return when (change.changeType) {
            ProductChangeType.PRICE_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        val oldPrice = change.oldValue.toDoubleOrNull()
                        val newPrice = change.newValue.toDoubleOrNull()
                        if (oldPrice != null && newPrice != null) {
                            val diff = newPrice - oldPrice
                            val symbol = if (diff > 0) "↗" else "↘"
                            "价格: ¥${change.oldValue} → ¥${change.newValue} $symbol"
                        } else {
                            "价格: ${change.oldValue} → ${change.newValue}"
                        }
                    }

                    change.newValue.isNotEmpty() -> "价格: ¥${change.newValue}"
                    else -> "价格变化"
                }
            }

            ProductChangeType.STOCK_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        "库存: ${change.oldValue} → ${change.newValue}"
                    }

                    change.newValue.isNotEmpty() -> "库存: ${change.newValue}"
                    else -> "库存变化"
                }
            }

            ProductChangeType.AVAILABILITY_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        val oldStatus = if (change.oldValue == "1") "可用" else "不可用"
                        val newStatus = if (change.newValue == "1") "可用" else "不可用"
                        "可用性: $oldStatus → $newStatus"
                    }

                    change.newValue.isNotEmpty() -> {
                        val status = if (change.newValue == "1") "可用" else "不可用"
                        "可用性: $status"
                    }

                    else -> "可用性变化"
                }
            }

            ProductChangeType.SECKILL_STATUS_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        val oldStatus = if (change.oldValue == "1") "是" else "否"
                        val newStatus = if (change.newValue == "1") "是" else "否"
                        "秒杀: $oldStatus → $newStatus"
                    }

                    change.newValue.isNotEmpty() -> {
                        val status = if (change.newValue == "1") "是" else "否"
                        "秒杀: $status"
                    }

                    else -> "秒杀状态变化"
                }
            }

            ProductChangeType.RESTRICT_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        "限购: ${change.oldValue} → ${change.newValue}"
                    }

                    change.newValue.isNotEmpty() -> "限购: ${change.newValue}"
                    else -> "限购变化"
                }
            }

            ProductChangeType.INFO_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        "${change.fieldName}: ${change.oldValue} → ${change.newValue}"
                    }

                    change.newValue.isNotEmpty() -> "${change.fieldName}: ${change.newValue}"
                    else -> "信息变化"
                }
            }

            ProductChangeType.OTHER_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        "${change.fieldName}: ${change.oldValue} → ${change.newValue}"
                    }

                    change.newValue.isNotEmpty() -> "${change.fieldName}: ${change.newValue}"
                    else -> "其他变化"
                }
            }
        }
    }

    /**
     * 发送测试通知（用于调试）
     */
    fun sendTestNotification(context: Context) {
        try {
            sendChangeNotification(
                context,
                "测试通知",
                "这是一个测试通知，用于验证通知系统是否正常工作",
                1
            )
            Log.d(TAG, "Test notification sent")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send test notification", e)
        }
    }

    /**
     * 清除所有监控通知
     */
    fun clearAllNotifications(context: Context) {
        try {
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            // 清除监控相关的通知（ID范围 2000-2999）
            for (i in NOTIFICATION_ID_BASE until NOTIFICATION_ID_BASE + 1000) {
                notificationManager.cancel(i)
            }
            Log.d(TAG, "All monitoring notifications cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear notifications", e)
        }
    }
}
